import { Button } from 'antd';
import styles from './index.module.scss';
import search_icon from '@/Resources/filmWall/search_icon.png';
import { PreloadImage } from '@/components/Image';
import { useCallback, useEffect, useRef, useState } from 'react';
import TVFocusable from '../TVFocus';
import { useDebounce, useFocusWithin, useInViewport, useUpdateEffect } from 'ahooks';
import FilmCard, { IFilmCard } from '@/components/FATWall_PC/FilmCard';
import { useRequest } from 'ahooks';
import { getMediaListFromRecommend, searchLocalMedia } from '@/api/fatWall';
import { defaultPageParam } from '../../FATWall_APP/Recently';
import ErrorComponentTV from '../Error';

const defaultValue = '支持全拼或者首字母搜索';

const keyboardChat = [
  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R',
  'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0'
];

export const defaultTypeList = [
  { key: 'all', title: '全部' },
  { key: 'movie', title: '电影' },
  { key: 'tv', title: '电视剧' },
]

interface IWantFindFilms {
  label: string;
  value: string;
  name: string;
}

const GuessWantSearch = (props: { defaultSearchItems: IFilmCard[] }) => {
  const { defaultSearchItems } = props;

  return (
    <div className={styles.gws_container}>
      <div className={styles.gws_title}>猜你想搜</div>
      <div className={styles.gws_content}>
        {
          defaultSearchItems.map((item, index) => (
            <FilmCard key={item.name} {...item} row={Math.floor(index / 6)} col={index % 6 + 8} id={`tv-id-searchItem-${item.name}`} onClick={() => console.log(item)} />
          ))
        }
      </div>
    </div>
  )
}

const SearchResult = (props: {
  searchItems: IFilmCard[], wantFinds: IWantFindFilms[], filters: string, setFilters: (v: string) => void, hasMore: boolean, loaderRef: React.RefObject<HTMLDivElement>
}) => {
  const { searchItems, wantFinds, filters, setFilters, loaderRef, hasMore } = props;

  const [collapse, setCollapse] = useState<boolean>(false); // 查询框折叠
  // 当焦点汇聚在输入显示框时，折叠状态变为展开
  const handleCurrentItem = () => setCollapse(false);

  const handleNotCurrentItem = () => setCollapse(true);

  // 根据模糊搜索的数据进行详细搜索
  const search = useCallback((item: IWantFindFilms) => {
    console.log(item);
  }, [])

  // 搜索结果确认回调
  const handleEnterSearchItem = useCallback((item: IFilmCard) => {
    console.log(item);
  }, [])

  return (
    <div className={styles.result_container}>
      <div className={`${styles.result_left} ${collapse ? styles.collapse : styles.expand}`}>
        <span>您可能在找</span>
        <div className={styles.result_want_find_content}>
          {
            wantFinds.map((item, index) => {
              return <TVFocusable key={item.name + index} onClick={() => search(item)} className={styles.result_want_find_content_item} id={`tv-id-searchItem-${item.name + index}`} row={index} col={8} currentItem={handleCurrentItem}>
                <span>{item.label}</span>
              </TVFocusable>
            })
          }
        </div>
      </div>
      <div className={styles.result_right}>
        <div className={styles.header}>
          <div className={styles.category_container}>
            {
              defaultTypeList.map((it, i) => (
                <TVFocusable id={`tv-id-searchItem-${it.key}`} row={0} col={9 + i} key={it.key} className={`${styles.tabs_item} ${filters === it.key ? styles.selected : ''}`} onClick={() => setFilters(it.key)} currentItem={handleNotCurrentItem}>
                  <span>{it.title}</span>
                </TVFocusable>
              ))
            }
          </div>
        </div>
        <div className={styles.content}>
          {
            searchItems.map((item, index) => (
              <FilmCard key={item.name} {...item} row={Math.floor(index / 6) + 10} col={index % 6 + 10} id={`tv-id-searchItem-${item.name}`} currentItemCallback={handleNotCurrentItem} onClick={() => handleEnterSearchItem(item)} /> // 卡片沉底，防止行数不够时向下光标自动选中搜索栏
            ))
          }

          {
            hasMore && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)
          }
        </div>
      </div>
    </div>
  )
}

const SearchByTV = () => {
  const [searchInput, setSearchInput] = useState<string>(defaultValue); // 查询值
  const search_input_value_ref = useRef<string>(defaultValue);
  const [searchItems, setSearchItems] = useState<IFilmCard[]>([]); // 搜索结果
  const [defaultSearchItems, setDefaultSearchItems] = useState<IFilmCard[]>([]); // 搜索结果
  const search_input_ref = useRef<HTMLDivElement>(null); // 输入显示框的引用
  const isFocusSearchInputRef = useRef(null);// 输入显示框容器的引用
  const [collapse, setCollapse] = useState<boolean>(false); // 查询框折叠
  const [searchIsError, setSearchIsError] = useState<boolean>(false); // 查询结果是否出错
  const [wantFinds, setWantFinds] = useState<IWantFindFilms[]>([]); // 模糊搜索的猜你想搜

  const { runAsync } = useRequest(getMediaListFromRecommend, { manual: true });
  const { runAsync: localRunAsync } = useRequest(searchLocalMedia, { manual: true })

  const [filters, setFilters] = useState<string>('all');

  // 加载更多的必要参数
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(true);
  const pageOptRef = useRef<{ offset: number, limit: number }>(defaultPageParam); // 分页参数


  const getRecommendData = useCallback(async () => {
    const res = await runAsync({ count: 12 }, { loadingMode: 'icon' }).catch((e) => console.log(`获取推荐影视失败！:${e}`));
    if (res && res.code === 0) {
      const temp = res.data.medias.map((item) => {
        return {
          url: item.poster.length > 0 ? item.poster[0] : '',
          score: item.score ? item.score.toFixed(1).toString() : '暂未评分',
          name: item.trans_name,
          title: item.trans_name,
          favourite: item.favourite === 1 ? true : false
        }
      })

      setDefaultSearchItems(temp);
      setSearchIsError(false);
      return;
    }
    setSearchIsError(true);
  }, [runAsync])

  const getLocalMedia = useCallback(async (callback: (v: IFilmCard[]) => void, filters?: string) => {
    if (search_input_value_ref.current === defaultValue) return;
    const classes = filters ? filters === 'all' ? undefined : filters === 'tv' ? '电视剧' : '电影' : undefined;

    const res = await localRunAsync({ keyword: search_input_value_ref.current, filter: { ...pageOptRef.current, classes: classes } }).catch((e) => console.log(`获取本地影视失败！:${e}`));
    if (res && res.code === 0) {
      if (!res.data) return;
      if (res.data.count && res.data.count < pageOptRef.current.limit) setHasMore(false);

      const temp = res.data.medias.map((item) => {
        return {
          url: item.poster.length > 0 ? item.poster[0] : '',
          score: item.score ? item.score.toFixed(1).toString() : '暂未评分',
          name: item.trans_name,
          title: item.trans_name,
          favourite: item.favourite === 1 ? true : false
        }
      })

      setWantFinds(temp.slice(0, 8).map((item) => {
        return {
          label: item.title,
          value: item.title,
          name: item.title,
        }
      }))

      callback(temp);
      setSearchIsError(false);
      return;
    }
    setSearchIsError(true);
  }, [localRunAsync])

  useEffect(() => {
    getRecommendData() // 获取推荐数据，用于猜你想搜
  }, [getRecommendData])

  useUpdateEffect(() => {
    getLocalMedia(v => setSearchItems(v), filters) // 获取本地影视数据，用于搜索结果展示
  }, [getLocalMedia, filters])

  useUpdateEffect(() => {
    if (searchInput) {
      search_input_value_ref.current = searchInput;
      if (search_input_value_ref.current === defaultValue) {
        setSearchItems([]);
        getRecommendData();

        return;
      }

      setHasMore(true);
      pageOptRef.current = { ...defaultPageParam };
      getLocalMedia(v => setSearchItems(v), filters);
    }
  }, [searchInput])

  useFocusWithin(isFocusSearchInputRef, {
    onBlur: () => {
      setCollapse(true);
    }
  });

  const inputEnter = useDebounce((string: string) => {
    // 设置输入显示
    setSearchInput(p => {
      if (p === defaultValue) {
        return string;
      }
      return p + string;
    })
  }, { wait: 300 })

  /**
   * 删除搜索输入框中的最后一个字符
   */
  const inputDelete = () => {
    setSearchInput(p => {
      if (p === defaultValue) return p;
      const str = p.slice(0, -1);
      if (str === '') return defaultValue;
      return str;
    })
  }

  // 输入显示改变的时候如果出现滚动条则滚动到最右
  useEffect(() => {
    const search_item = search_input_ref.current;
    if (!search_item) return;
    search_item.scrollLeft = search_item.scrollWidth;
  }, [searchInput])

  // 当焦点汇聚在输入显示框时，折叠状态变为展开
  const handleCurrentItem = useCallback(() => setCollapse(false), []);

  // 刷新数据
  const clearAndRefresh = useCallback(() => {
    setSearchInput(defaultValue);
    setSearchItems([]);

    getRecommendData();
  }, [getRecommendData])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.2,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      pageOptRef.current = { ...pageOptRef.current, offset: pageOptRef.current.offset + pageOptRef.current.limit };
      getLocalMedia((data) => setSearchItems(p => [...p, ...data]), filters);
    }
  }, [inViewport, getLocalMedia])

  return (
    <div className={styles.container}>
      <div className={`${styles.search_container}  ${collapse ? styles.collapse : styles.expand}`} ref={isFocusSearchInputRef}>
        <div className={styles.search_input}>
          <PreloadImage src={search_icon} alt='search' />
          <div className={styles.search_input_item} ref={search_input_ref}>{searchInput}</div>
        </div>
        <div className={styles.search_buttons}>
          <TVFocusable className={styles.search_buttons_item} id={`tv-id-search-clear`} row={0} col={0} onClick={clearAndRefresh} children={<Button type='text'>清空</Button>} currentItem={handleCurrentItem} />
          <TVFocusable className={styles.search_buttons_item} id={`tv-id-search-delete`} row={0} col={5} onClick={inputDelete} children={<Button type='text'>删除</Button>} currentItem={handleCurrentItem} />
        </div>
        <div className={styles.keyboard_container}>
          {
            keyboardChat.map((it, i) => (
              <TVFocusable className={styles.keyboard_item} key={it} id={`tv-id-search-${it}`} row={Math.floor(i / 6) + 1} col={i % 6}
                children={it} onClick={() => inputEnter(it)} currentItem={handleCurrentItem} />
            ))
          }
        </div>
      </div>
      <div className={styles.search_item_container}>
        <ErrorComponentTV isError={searchIsError} hasContent={searchItems.length !== 0} hasLibrary={true} refresh={clearAndRefresh}
          text={searchIsError ? '搜索失败，请重试' : '没搜索到相关结果'} subText={''} customizeRow={1} customizeCol={12}>
          {
            searchInput === defaultValue ?
              <GuessWantSearch defaultSearchItems={defaultSearchItems} /> :
              <SearchResult loaderRef={loaderRef} hasMore={hasMore} filters={filters} setFilters={setFilters} wantFinds={wantFinds} searchItems={searchItems} /> /* 当搜索值为默认值时，显示猜你想搜，否则显示搜索结果页面 */
          }
        </ErrorComponentTV>
      </div>
    </div>
  )
}

export default SearchByTV;