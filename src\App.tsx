import React, { Suspense, useCallback, useEffect } from "react";
import { Route, Switch } from "react-router-dom";
import { DotLoading } from "antd-mobile";
import routers, { IRouter } from "./router";
import setRootFontSize from "./utils/setRootFontSize";
import { useTheme } from "./utils/themeDetector";
import { getDeviceType, fetchAppInfo, fetchWebDavInfo, fetchAccountInfo, getSystemType } from "./utils/DeviceType";
import IPCLayout from "./layouts/Layout";
import { removeMicroPcListener } from "./utils/microAppUtils";
import { FocusProvider } from "./pages/FATWall/FATWall_TV/TVFocus";

const Loading = () => (
  <div
    style={{
      display: "flex",
      flexDirection: "column",
      justifyContent: "center",
      alignItems: "center",
      height: "100vh",
      backgroundColor: "#f5f5f5",
    }}
  >
    <DotLoading color="primary" />
    <div style={{ marginTop: 16, color: "#999" }}>加载中...</div>
  </div>
);

const App: React.FC = () => {
  const { toggleTheme } = useTheme(); // 主题切换函数

  // 定义一个切换主题函数给hik调用
  const changeTheme = useCallback((theme: 'dark' | 'light') => {
    console.log('app调用切换主题:', theme);
    toggleTheme(theme);
  }, [toggleTheme]);

  useEffect(() => {
    // 设置根元素字体大小
    setRootFontSize();
    const initializeApp = async () => {
      try {
        await fetchAppInfo(); // 异步加载设备信息
        await fetchWebDavInfo(); // 加载webDav
        await fetchAccountInfo(); // 加载账号信息

        const os = getSystemType(); // 获取设备类型信息
        switch (os) {
          case 'windows':
            window.onetrack?.('init', ***********);
            break;
          case 'android':
            window.onetrack?.('init', ***********);
            break;
          case 'ios':
            window.onetrack?.('init', ***********);
            break;
          case 'macos':
            window.onetrack?.('init', ***********);
            break;
          default:
            console.warn(`未知的设备类型`);
        }
      } catch (error) {
        console.log("初始化应用时获取设备信息失败:", error);
      }
    };

    initializeApp();
    window.addEventListener("resize", setRootFontSize);
    return () => {
      window.removeEventListener("resize", setRootFontSize);
      //移除PC 监听
      removeMicroPcListener();
    };
  }, []);

  useEffect(() => {
    window.hs_registerHandler?.('changeTheme', changeTheme);
  }, [changeTheme])

  // 检查是否需要使用布局
  const shouldUseLayout = (): boolean => {
    const deviceType = getDeviceType();
    return deviceType === 1;
  };

  const renderRoutes = (routes: IRouter[], parentPath = "", depth = 0) => {
    //只在最外层运用这个布局
    const shouldApplyLayout = depth === 0;

    return (
      <Switch>
        <FocusProvider>
          {routes.map((route, index) => {
            const useLayout = shouldUseLayout();
            // 规范化路径拼接（处理首尾斜杠）
            const normalizedParent = parentPath.endsWith("/")
              ? parentPath.slice(0, -1)
              : parentPath;
            const normalizedChild = route.path.startsWith("/")
              ? route.path
              : `/${route.path}`;
            const fullPath = `${normalizedParent}${normalizedChild}`;

            return (
              <Route
                key={fullPath}
                path={fullPath}
                exact={route.exact}
                render={(props) => {
                  const Component = route.component;
                  return (
                    <Suspense fallback={<Loading />}>
                      {shouldApplyLayout && useLayout && route.path !== "/componentCard" ? (
                        <IPCLayout>
                          <Component {...props}>
                            {route.children && renderRoutes(route.children, fullPath, depth + 1)}
                          </Component>
                        </IPCLayout>
                      ) : (
                        <Component {...props}>
                          {route.children && renderRoutes(route.children, fullPath, depth + 1)}
                        </Component>
                      )}
                    </Suspense>
                  );
                }}
              />
            );
          })}
        </FocusProvider>
      </Switch>
    );
  };

  return <Suspense fallback={<Loading />}>{renderRoutes(routers)}</Suspense>;
};

export default App;
